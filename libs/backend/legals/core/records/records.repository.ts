import { RecordNew, RecordUpdate } from './record';
import { LegalRecord } from '@mynotary/crossplatform/records/api';

export abstract class RecordsRepository {
  abstract getLegalRecords(recordIds: string[]): Promise<LegalRecord[]>;

  abstract createRecord(record: RecordNew): Promise<{ id: string }>;

  abstract updateRecord(record: RecordUpdate): Promise<void>;

  abstract deleteRecord(recordId: string): Promise<void>;
}
