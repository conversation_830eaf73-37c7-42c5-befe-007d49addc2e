import { SignatureWebhookController } from '@mynotary/backend/signatures/feature';
import { v4 as uuid } from 'uuid';
import { YousignSignatureRequestEventDto, YousignSignerEventDto } from '@mynotary/crossplatform/api-signatures/openapi';
import { EmailsApiService, EmailTemplateId, SignatureErrorPostActivation } from '@mynotary/backend/emails/api';
import { SignatureRegisterUpdaterService, SignaturesService } from '@mynotary/backend/signatures/core';
import { EventsApiService } from '@mynotary/backend/events/api';
import { NotificationApiService, SignatureNotificationType } from '@mynotary/backend/notifications/api';
import { RegisterEntryStatus } from '@mynotary/backend/registers/api';
import { provideSignaturesTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';

describe(SignatureWebhookController.name, () => {
  it('should handle the signature_request.done event', async () => {
    const {
      client,
      completeRegisterEntrySpy,
      createEventsSpy,
      emailServiceSpy,
      member,
      notificationServiceSpy,
      testingRepos
    } = await setup();

    const { id: operationId } = await testingRepos.operations.createVenteAncien({
      labelOperation: 'Dossier de vente',
      organizationId: member.organizationId,
      userId: member.userId
    });
    const { id: contractId } = await testingRepos.contracts.createMandat({ operationId, userId: member.userId });

    const { id: featureId } = await testingRepos.features.createFeature({
      organizationId: member.organizationId,
      type: FeatureType.TRANSACTION_REGISTER_ACCESS
    });

    await testingRepos.register.createRegisterEntry({
      contractId,
      featureId,
      organizationId: member.organizationId,
      status: RegisterEntryStatus.CLOSED,
      userId: member.userId
    });

    const { id: entryId } = await testingRepos.register.createRegisterEntry({
      contractId,
      featureId,
      organizationId: member.organizationId,
      status: RegisterEntryStatus.RESERVED,
      userId: member.userId
    });

    const { id: signatureId } = await testingRepos.signatures.createActiveSignature({
      contractId,
      operationId,
      userId: member.userId
    });

    const providerId = uuid();
    const providerFileId = uuid();

    const fakeSignatoryEmail = '<EMAIL>';
    const subscriberMember = await testingRepos.createMember({});

    const { id: signatoryId } = await testingRepos.signatures.createSignatory({
      email: fakeSignatoryEmail,
      providerId,
      signatureId
    });

    await testingRepos.signatures.createSubscriber({
      email: subscriberMember.userEmail,
      signatureId
    });

    const file = await testingRepos.signatures.createSignatureFile({
      providerId: providerFileId,
      signatureId
    });

    const body = {
      data: {
        signature_request: {
          documents: [
            {
              id: providerFileId
            }
          ],
          external_id: signatureId,
          id: signatureId,
          signers: [
            {
              id: providerId
            }
          ]
        }
      },
      event_id: 'signature_request.done',
      event_name: 'signature_request.done',
      event_time: new Date().toISOString(),
      sandbox: true
    } satisfies YousignSignatureRequestEventDto;

    const response = await client.post(`/signature-yousign`).send(body);

    const contract = await testingRepos.contracts.getContract(contractId);
    const signatory = await testingRepos.signatures.findSignatory(signatoryId);
    const signatureFile = await testingRepos.signatures.findSignatureFile(file.id);
    const signature = await testingRepos.signatures.findSignature(signatureId);

    const entry = await testingRepos.register.getRegisterEntry(entryId.toString());

    expect(completeRegisterEntrySpy).toHaveBeenCalledTimes(1);
    expect(entry.status).toBe(RegisterEntryStatus.VALIDATED);
    expect(emailServiceSpy).toHaveBeenCalledTimes(3);
    expect(emailServiceSpy).toHaveBeenNthCalledWith(1, {
      data: {
        appUrl: expect.stringMatching(/\/operation\/\d+\/signatures-actives\/\d+/),
        contractId,
        documentName: 'Dossier de vente-test',
        isMynotaryUser: true,
        operationId: operationId,
        operationLabel: 'Dossier de vente',
        organizationId: member.organizationId,
        organizationName: 'MyNotary',
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: 'SIGNATURE_COMPLETED'
      },
      receiver: member.userEmail
    });

    expect(emailServiceSpy).toHaveBeenNthCalledWith(2, {
      data: {
        appUrl: expect.stringMatching(/\/signatures\?signatureId=\d+&token=[A-Za-z0-9.\-_]+/),
        contractId,
        documentName: 'test',
        isMynotaryUser: false,
        operationId: operationId,
        operationLabel: 'Dossier de vente',
        organizationId: member.organizationId,
        organizationName: 'MyNotary',
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: 'SIGNATURE_COMPLETED'
      },
      receiver: fakeSignatoryEmail
    });

    expect(emailServiceSpy).toHaveBeenNthCalledWith(3, {
      data: {
        appUrl: expect.stringMatching(/\/signatures\?signatureId=\d+&token=[A-Za-z0-9.\-_]+/),
        contractId,
        documentName: 'Dossier de vente-test',
        isMynotaryUser: true,
        operationId: operationId,
        operationLabel: 'Dossier de vente',
        organizationId: member.organizationId,
        organizationName: 'MyNotary',
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: 'SIGNATURE_COMPLETED'
      },
      receiver: subscriberMember.userEmail
    });

    expect(signatory?.proofFileId).toBeDefined();
    expect(signatureFile?.toSignFileId).toBeDefined();
    expect(signatureFile?.name).toEqual('Document à signer');
    expect(signature?.signatureTime).toBeDefined();
    expect(createEventsSpy).toHaveBeenCalledTimes(1);
    expect(createEventsSpy).toHaveBeenCalledWith({
      data: { organizationId: member.organizationId, signatureId, userId: member.userId },
      eventType: 'SIGNATURE_COMPLETED',
      organizationId: member.organizationId
    });
    expect(notificationServiceSpy).toHaveBeenNthCalledWith(1, {
      contractId,
      operationId,
      signatureId,
      signatureTime: expect.any(String),
      type: SignatureNotificationType.SIGNATURE_COMPLETED,
      userIds: [member.userId, subscriberMember.userId]
    });
    expect(contract.status).toBe(ContractStatus.SIGNATURE_COMPLETED);
    expect(response.status).toBe(200);
  });

  it('should handle the signature_request.expired event', async () => {
    const { client, emailServiceSpy, member, notificationServiceSpy, testingRepos } = await setup();

    const { id: operationId } = await testingRepos.operations.createVenteAncien({
      organizationId: member.organizationId,
      userId: member.userId
    });
    const { id: contractId } = await testingRepos.contracts.createMandat({ operationId, userId: member.userId });
    const { id: signatureId } = await testingRepos.signatures.createActiveSignature({
      contractId,
      operationId,
      userId: member.userId
    });

    const providerId = uuid();
    const providerFileId = uuid();

    const fakeSignatoryEmail = '<EMAIL>';
    const fakeSignatory2Email = '<EMAIL>';

    const { id: creditId } = await testingRepos.billings.createCredits({
      featureState: { signatureCredits: { number: 1 } },
      featureType: FeatureType.SIGNATURE_CREDITS,
      organizationId: member.organizationId
    });

    await testingRepos.signatures.createSignatory({
      email: fakeSignatoryEmail,
      providerId,
      signatureId,
      signatureTime: new Date()
    });

    await testingRepos.signatures.createSignatory({
      email: fakeSignatory2Email,
      providerId,
      signatureId
    });

    const body = {
      data: {
        signature_request: {
          documents: [
            {
              id: providerFileId
            }
          ],
          external_id: signatureId,
          id: signatureId,
          signers: [
            {
              id: providerId
            }
          ]
        }
      },
      event_id: 'signature_request.expired',
      event_name: 'signature_request.expired',
      event_time: new Date().toISOString(),
      sandbox: true
    } satisfies YousignSignatureRequestEventDto;

    const response = await client.post(`/signature-yousign`).send(body);

    const creditNumber = await testingRepos.billings.getCreditNumber({
      creditId
    });

    const history = await testingRepos.billings.getCreditHistory({
      organizationId: member.organizationId
    });

    const contract = await testingRepos.contracts.getContract(contractId);
    const signature = await testingRepos.signatures.findSignature(signatureId);

    expect(creditNumber).toBe(1);
    expect(history).toMatchObject([]);
    expect(emailServiceSpy).toHaveBeenCalledTimes(1);
    expect(emailServiceSpy).toHaveBeenNthCalledWith(1, {
      data: {
        appUrl: expect.stringMatching(/\/operation\/\d+\/signatures-actives\/\d+/),
        contractId: contractId,
        documentLabel: 'test',
        expirationTime: expect.anything(),
        missingSignatories: [
          {
            firstname: 'John',
            lastname: 'Doe'
          }
        ],
        operationId: operationId,
        organizationId: member.organizationId,
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: 'SIGNATURE_DEADLINE'
      },
      receiver: member.userEmail
    });
    expect(signature?.expirationTime).toBeDefined();
    expect(signature?.cancelationTime).toBeDefined();
    expect(contract.status).toBe(ContractStatus.SIGNATURE_EXPIRED);
    expect(notificationServiceSpy).toHaveBeenNthCalledWith(1, {
      contractId,
      expirationTime: expect.any(String),
      operationId,
      signatureId,
      type: SignatureNotificationType.SIGNATURE_EXPIRED,
      userIds: [member.userId]
    });
    expect(response.status).toBe(200);
  });

  it('should handle a signer.done event with multiple signatories', async () => {
    const { client, emailServiceSpy, member, notificationServiceSpy, testingRepos } = await setup();

    const { id: operationId } = await testingRepos.operations.createVenteAncien({
      labelOperation: 'Dossier de vente',
      organizationId: member.organizationId,
      userId: member.userId
    });
    const { id: contractId } = await testingRepos.contracts.createMandat({ operationId, userId: member.userId });
    const { id: signatureId } = await testingRepos.signatures.createActiveSignature({
      contractId,
      creationContext: {
        dueDate: new Date().toISOString(),
        ordered: true
      },
      emailTemplate: {
        content: 'content {{signatureLink}}',
        subject: 'subject'
      },
      operationId,
      shouldNotifyAfterSignature: true,
      userId: member.userId
    });

    const signatoryProviderId = uuid();
    const secondSignatoryProviderId = uuid();

    const { id: firstSignatoryId } = await testingRepos.signatures.createSignatory({
      email: member.userEmail,
      order: 0,
      providerId: signatoryProviderId,
      signatureId
    });

    const { id: secondSignatory } = await testingRepos.signatures.createSignatory({
      email: member.userEmail,
      order: 1,
      providerId: secondSignatoryProviderId,
      signatureId
    });

    const body = {
      data: {
        signature_request: {
          documents: [],
          external_id: signatureId,
          id: signatureId,
          signers: [
            {
              id: signatoryProviderId
            },
            {
              id: secondSignatoryProviderId
            }
          ]
        },
        signer: {
          id: signatoryProviderId
        }
      },
      event_id: 'signer.done',
      event_name: 'signer.done',
      event_time: new Date().toISOString(),
      sandbox: true
    } satisfies YousignSignerEventDto;

    const response = await client.post(`/signature-yousign`).send(body);

    const signatory = await testingRepos.signatures.findSignatory(firstSignatoryId);
    const nextSignatory = await testingRepos.signatures.findSignatory(secondSignatory);

    expect(response.status).toBe(200);
    expect(signatory?.signatureTime).toBeTruthy();
    expect(nextSignatory?.lastReminderTime).toBeTruthy();
    expect(notificationServiceSpy).toHaveBeenNthCalledWith(1, {
      contractId,
      operationId,
      signatoryFullName: `${signatory?.firstname} ${signatory?.lastname}`,
      signatoryId: firstSignatoryId,
      signatureId,
      signatureTime: expect.any(String),
      type: SignatureNotificationType.SIGNATURE_SIGNATORY_SIGNED,
      userIds: [member.userId]
    });
    expect(emailServiceSpy).toHaveBeenCalledTimes(2);
    expect(emailServiceSpy).toHaveBeenNthCalledWith(1, {
      data: {
        appUrl: expect.stringMatching(/\/operation\/\d+\/signatures-actives\/\d+/),
        contractId: contractId,
        documentLabel: 'test',
        operationId: operationId,
        operationLabel: 'Dossier de vente',
        organizationId: member.organizationId,
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        signatory: {
          firstname: 'John',
          lastname: 'Doe'
        },
        subject: `Dossier de vente - Un signataire vient de signer le contrat test`,
        templateId: 'SIGNATURE_SIGNATORY_SIGNED'
      },
      receiver: member.userEmail
    });
    expect(emailServiceSpy).toHaveBeenNthCalledWith(2, {
      data: {
        contractId: contractId,
        emailContent: `content signature_link_${secondSignatoryProviderId}`,
        operationId: operationId,
        organizationId: member.organizationId,
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR',
          organizationName: 'MyNotary',
          phone: ''
        },
        subject: 'Dossier de vente - Vous êtes invité(e) à signer des documents pour le contrat Mandat Simplifié',
        templateId: 'SIGNATURE_REQUEST'
      },
      receiver: member.userEmail
    });
  });

  it('should handle a signer.error event', async () => {
    const { client, emailServiceSpy, member, notificationServiceSpy, testingRepos } = await setup();

    const { id: operationId } = await testingRepos.operations.createVenteAncien({
      organizationId: member.organizationId,
      userId: member.userId
    });
    const { id: contractId } = await testingRepos.contracts.createMandat({ operationId, userId: member.userId });
    const { id: signatureId } = await testingRepos.signatures.createActiveSignature({
      contractId,
      creationContext: {
        dueDate: new Date().toISOString(),
        ordered: true
      },
      emailTemplate: {
        content: 'content',
        subject: 'subject'
      },
      operationId,
      shouldNotifyAfterSignature: true,
      userId: member.userId
    });

    const signatoryProviderId = uuid();

    const { id: creditId } = await testingRepos.billings.createCredits({
      featureState: { signatureCredits: { number: 1 } },
      featureType: FeatureType.SIGNATURE_CREDITS,
      organizationId: member.organizationId
    });

    const body = {
      data: {
        signature_request: {
          documents: [],
          external_id: signatureId,
          id: signatureId,
          signers: [
            {
              id: signatoryProviderId
            }
          ]
        },
        signer: {
          id: signatoryProviderId
        }
      },
      event_id: 'signer.error',
      event_name: 'signer.error',
      event_time: new Date().toISOString(),
      sandbox: true
    } satisfies YousignSignerEventDto;

    const response = await client.post(`/signature-yousign`).send(body);

    expect(response.status).toBe(200);

    const creditNumber = await testingRepos.billings.getCreditNumber({
      creditId
    });
    expect(creditNumber).toBe(1);

    const history = await testingRepos.billings.getCreditHistory({
      organizationId: member.organizationId
    });
    expect(history).toMatchObject([]);

    const contract = await testingRepos.contracts.getContract(contractId);
    expect(contract.status).toBe(ContractStatus.SIGNATURE_ERROR);

    const signature = await testingRepos.signatures.findSignature(signatureId);
    expect(signature?.expirationTime).toBeDefined();
    expect(signature?.cancelationTime).toBeDefined();

    expect(emailServiceSpy).toHaveBeenCalledTimes(1);
    expect(emailServiceSpy).toHaveBeenNthCalledWith(1, {
      data: {
        emailContent: expect.any(String),
        subject: 'Signature annulée',
        templateId: EmailTemplateId.SIGNATURE_ERROR_POST_ACTIVATION
      } satisfies SignatureErrorPostActivation,
      receiver: member.userEmail
    });

    expect(notificationServiceSpy).toHaveBeenNthCalledWith(1, {
      cancelationTime: expect.any(String),
      contractId,
      newSignatureId: expect.any(String),
      operationId,
      reason: 'Erreur prestataire de signature',
      signatureId,
      type: SignatureNotificationType.SIGNATURE_SIGNATORY_ERROR,
      userIds: [member.userId]
    });
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,

      controller: SignatureWebhookController,
      providers: provideSignaturesTest()
    });

    const testingRepos = getService(TestingRepositories);
    const emailApiService = getService(EmailsApiService);
    const notificationApiService = getService(NotificationApiService);
    const signaturesService = getService(SignaturesService);
    const signatureRegisterUpdaterService = getService(SignatureRegisterUpdaterService);

    const emailServiceSpy = jest.spyOn(emailApiService, 'sendEmail').mockResolvedValue();
    const notificationServiceSpy = jest.spyOn(notificationApiService, 'createNotification').mockResolvedValue();

    jest.spyOn(signaturesService, 'syncFirebaseSignature').mockResolvedValue();

    const completeRegisterEntrySpy = jest.spyOn(signatureRegisterUpdaterService, 'completeRegisterEntry');
    const cancelRegisterEntrySpy = jest.spyOn(signatureRegisterUpdaterService, 'cancelRegisterEntry');

    const eventApiService = getService(EventsApiService);
    const createEventsSpy = jest.spyOn(eventApiService, 'createEvents').mockResolvedValue();

    const member = await testingRepos.createMember({});

    return {
      cancelRegisterEntrySpy,
      client,
      completeRegisterEntrySpy,
      createEventsSpy,
      emailServiceSpy,
      member,
      notificationServiceSpy,
      testingRepos
    };
  }
});
