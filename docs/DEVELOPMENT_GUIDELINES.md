# Development Guidelines

## Creating New Features

When developing new features, we follow a spec first and test-driven approach following the steps described below.

### 1. Specifications and Planning

Every new feature request goes through a technical specification phase conducted with all developers present. This phase
is crucial for identifying all necessary modifications and estimating the development timeline.

During this phase, we identify:

- **Affected Applications**: Which applications will be impacted by the feature and which OpenAPI specifications need to
  be updated
- **Scopes**: Which scopes need to be updated or created
- **Endpoints**: Which endpoints need to be updated or created
- **Permissions**: Which permissions are required
- **Services**: Which services need to be created or modified
- **Database**: Which PostgreSQL tables or columns need to be created or modified
- **Tests**: Which tests need to be added (unit, integration, end-to-end)
- **Progressive Deployment**: How to deploy the feature incrementally

This phase allows us to clearly define requirements and acceptance criteria before beginning implementation, and to
estimate the time needed to complete the feature.

### 2. Boilerplate Creation

This step is done at the beginning of the implementation and the goal is to create all the necessary files, folders and
common models to implement the feature. It will make future implementation easier to review and reduce conflicts between
pull requests.

#### Specification and DTOs

Identify which API application will use the controller. Modify the corresponding `.openapi.yaml` file to define the new
endpoints and DTOs. Use `pnpm codegen` to create the necessary DTO classes that will be used in your controllers.

#### Database Schema

If your feature requires database changes :

1. Create a new changeset in the src/main/resources/db.changelog.xml on the legacy repository.
2. Apply the changeset using liquibase `mvn resources:resources liquibase:update`
3. Sync the change on prisma schema using `pnpm prisma-sync`. Keep only the necessary changes in the prisma schema.

#### Files generation

If the feature require a new scope or new library type, use `nx g libs` to generate the necessary files. Review the
change and add all the necessary files, services and methods. The body of the methods can be filled with
`throw new Error('Not implemented');`

#### Dependency injection and Providers

If the feature requires new services, do not forget to provide the services in the `backend/{scope}/providers`,
`frontend/{scope}/infra` and `apps/{app}/src/app.module.ts` or `apps/front-mynotary/src/main.tsx`.

### 3. Implementation

Write comprehensive tests before starting the implementation then iterate between implementation and testing until all
tests pass, refining your code as needed.

## File Types

File name must be in kebab-case.

| Usage           | Extension             | Description                                                                                   |
| --------------- | --------------------- | :-------------------------------------------------------------------------------------------- |
| Controller      | `.controller.ts`      | Handles HTTP requests and responses, defines routes and endpoints for the API                 |
| DTO converters  | `.dto-converters.ts`  | Converts between scope models and Data Transfer Objects (DTOs) used in API requests/responses |
| Service         | `.service.ts`         | Contains business logic and orchestrates the use of repositories and other services           |
| Repository      | `.repository.ts`      | Defines the interface for data access operations in the core layer                            |
| Repository impl | `.repository.impl.ts` | Implements the repository interface using Prisma or other data access technologies            |
| Provider        | `.provider.ts`        | Defines the interface for external service integrations                                       |
| Provider impl   | `.provider.impl.ts`   | Implements the provider interface using specific external services or APIs                    |
| Client          | `.client.ts`          | Defines the interface for HTTP clients that connect to external services                      |
| Client impl     | `.client.impl.ts`     | Implements the client interface using specific HTTP libraries (Axios, Fetch, etc.)            |
| Utils           | `.utils.ts`           | Contains utility functions and helpers that can be used across multiple files                 |
| Fake            | `.fake.ts`            | Provides fake implementations of interfaces for testing purposes                              |
| Redux Slice     | `.slice.ts`           | Defines the state slice and reducers for Redux state management                               |
| Redux Selector  | `.selector(s).ts`     | Defines selectors to retrieve data from the Redux store                                       |
| Redux Thunk     | `.thunks.ts`          | Defines asynchronous actions that interact with the Redux store and perform side effects      |

### Service VS Repository VS Provider VS Client

- A Service is a class that handles our business logic and links a controller and other pieces of logics such as
  services from other domains, repositories and providers.
- A Repository is a class that connects our service with our database.
- A Provider is a class that connects our service with external APIs (e.g., `AuthenticationsProvider`,
  `ElectronicSignatureProvider`) or external tools (e.g., `ArchivesStreamingProvider`). It abstracts the interaction
  with these external systems.
- A Client is a class that handle the connection to an external api. (Should be faked for testing purposes)

## Naming Convention for Symbols

- Exported sympol must match file name (eg: `legal-branches.controller.ts` must have a `LegalBranchesController` class)
- Backend infra implementations must have `RepositoryImpl` suffix
- Repository and client methods prefixed by `get` must throw if not found
- Repository and client methods prefixed by `find` must return null if not found
- Frontend infra implementations must have `ClientImpl` suffix
- Environement variables must be in uppercase
- If a collection is a light version of a resource it must be suffixed by `Light`. eg: `OrganizationLight`
- if a method use more than 1 attribute, an object must be used. If a type is created, it must be prefixed by `Args` and
  defined at the end of the file
- Enum value must be uppercase and should be prefered over string literals

## Routes path creation

Routes must follow [zalando guidelines](https://opensource.zalando.com/restful-api-guidelines/#urls)

Routes must have 1 level: /resource/:id/sub-resources is invalid and should be replaced by /sub-resources/:id.

The type of object returned by the endpoint defines the scope and path of the route. The resource name in the path
should match the type of resource being returned, not the context in which it's being accessed. For example:

- If you're returning files filtered by a user ID, use `GET /files?userId={id}` instead of `GET /user-files?userId={id}`
- If you're returning organizations filtered by a user ID, use `GET /organizations?userId={id}` instead of
  `GET /user-organizations?userId={id}`

Examples:

- `POST /organizations` -> create a new organization resource
- `GET /organizations?organizationId={id}` -> read a single organization resource
- `GET /organizations` -> read a collection of organization resource
- `PATCH /organizations/:organizationId` -> update organization resource
- `POST /operations` -> create **operation** resource
- `GET /operations?id={id}` -> read a single operation resource
- `GET /operations` -> read a collections of operation resource
- `PATCH /operations/:id` -> update operation resource
- `POST /operation-transfers` -> tranfer an operation to another organization.,
- `POST /operation-transfer-validations`
  - this route currently doesn't exist but it's a good example of what would be the route to reject or validate an
    operation transfer
  - It's a recommended to have an explicit and unique name but sometimes it's hard so we used the context in the naming
    to make it explict

## Error handling

We follow a "fail fast and fail hard" approach to error handling. This means that errors should be detected and reported
as early as possible in the execution flow. The primary goal is to identify and fix errors quickly, rather than allowing
the application to continue running in a potentially inconsistent state.

Key principles of our error handling strategy:

- **Early Detection**: Validate inputs and preconditions at the beginning of functions to catch errors early
- **Clear Error Messages**: Provide detailed error messages that help identify the root cause
- **Proper Error Types**: Use the appropriate error types defined in the project (see [ERRORS.md](./ERRORS.md) for
  details)
- **Fail Fast**: Don't try to recover from unexpected errors; let them propagate to be handled by the global error
  handler
- **Logging**: Ensure errors are properly logged with relevant context information. Development errors are logged
  directly to the console, while preproduction and production errors are available in Google Cloud Platform Log Explorer

This approach helps us maintain a robust and reliable system by making errors visible and addressable as soon as they
occur. For more detailed information about our error handling system, including error types and best practices, refer to
the [ERRORS.md](./ERRORS.md) document.

## Best Practices

- Always follow the established patterns and respect the boundaries between layers
- Write unit tests for all business logic in the core layer
- Use dependency injection to make your code testable
- Handle errors appropriately using the error types defined in the project
- Document your code, especially public APIs and complex business logic
- Follow the repository's coding style and conventions

By adhering to these guidelines, you'll help maintain a clean, maintainable, and scalable codebase.
