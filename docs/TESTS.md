# Testing Guidelines

This document outlines the testing practices and conventions used in the MyNotary project. We employ a comprehensive
testing strategy that includes unit tests, wide tests (integration tests with database), and end-to-end tests to ensure
the quality and reliability of our application.

## Backend Tests

Backend tests are divided into two main categories: unit tests and wide tests.

### Unit Tests

Unit tests focus on testing individual components in isolation, without using a real database:

- Use mocks and fakes to isolate the service being tested
- Faster execution than wide tests
- Ideal for testing business logic and service methods
- Name your test files with `.spec.ts` suffix (without the `wide` part)

### Wide Tests

Wide tests are backend tests that use a real database connection. Each test file with the `.wide.spec.ts` suffix gets
its own isolated database instance to ensure test isolation and parallel execution.

#### Database Isolation

The database isolation is managed by the `jest.setup.ts` file, which is executed once per test file:

1. When a test file with `.wide.spec.ts` suffix is detected, a unique database name is generated using a UUID
2. A new database is created as a copy of the `mynotary_testing` template database
3. A Prisma client is initialized with a connection to this database
4. After all tests in the file complete, the database is automatically dropped

This approach provides several benefits:

- Tests can run in parallel without data conflicts
- Each test suite runs in complete isolation
- No need to clean up data between tests
- Prevents flaky tests caused by data leakage between test runs

#### Using TestingRepositories

For creating and managing test data in wide tests:

- Use the `TestingRepositories` utility class to create test entities
- All methods in TestingRepositories that create entities also handle their deletion
- Hard coded numeric values are forbidden in tests - use the primary keys of the created entities
- The only place where hard coded values are allowed is in TestingRepositories implementations

#### Creating Wide Tests

To create a wide test:

1. Name your test file with the `.wide.spec.ts` suffix (e.g., `your-feature.wide.spec.ts`)
2. Use the `createTestingWideApp` utility to set up your test environment
3. Get the `TestingRepositories` instance and any other services you need via the `getService` method
4. Use the returned `client` to make HTTP requests to your controller

_Example setup function:_

```typescript
async function setup() {
  const { client, getService } = await createTestingWideApp({
    bypassAuth: true, // Optional: bypass authentication
    controller: YourController,
    providers: provideYourFeatureTest()
  });

  const testingRepos = getService(TestingRepositories);
  const usersRepository = getService(UsersRepository);

  const organization = await testingRepos.organizations.createOrganization({ name: 'Test Org' });
  const user = await usersRepository.createUser({ email: '<EMAIL>' });

  return {
    client,
    user,
    organization,
    testingRepos
  };
}
```

### Running Backend Tests

To run tests for a specific scope:

```shell
nx run backend-{scope}-test:test --watch
```

## End-to-End Tests

End-to-end tests verify the entire application stack, including frontend and backend integration. We use Playwright to
write our end-to-end tests. You can find the Playwright documentation [here](https://playwright.dev/docs/intro). Tests
files are located in the `e2e` folder of the application (eg: `apps/front-mynotary/e2e`).

Since e2e tests are time-consuming to run and more challenging to maintain, we focus on implementing them only for the
most critical workflows. These are the workflows that are most frequently used by our users and could generate
significant support tickets and user frustration if they were to stop working. By prioritizing these critical paths, we
ensure that our most important user journeys remain functional while keeping the test suite maintainable.

### E2E Test Structure

Our e2e test implementation follows a structured approach with several key components:

#### Test Environment

The e2e tests can run in three different environments:

- **Local Development**: Uses localhost URLs for API endpoints
- **Preproduction**: Uses preproduction URLs for testing against the preproduction environment

The environment configuration is managed in `apps/front-mynotary/e2e/env.ts` which provides appropriate API URLs and IDs
based on the current environment.

#### Authentication

Authentication for e2e tests is handled through a token-based approach:

- Tests use pre-authenticated user states stored in JSON files
- Authentication setup is performed in `apps/front-mynotary/e2e/setup/auth.setup.ts`
- Different user profiles are available (standard user, admin user, user without organization, etc.)
- Authentication tokens are stored in the `.auth` directory and loaded for test runs
- The `test.use({ storageState: authUserStoragePath })` pattern is used to apply authentication to test cases

#### Page Objects

We use the Page Object Model pattern to organize our test code:

- **Page Classes**: Located in `apps/front-mynotary/e2e/pages/` represent entire application pages
- **Page Objects**: Located in `apps/front-mynotary/e2e/page-objects/` represent reusable components
- Page objects encapsulate UI interactions and provide a clean API for tests
- Page objects handle waiting for network responses and UI elements

Example of a page object:

```typescript
export class SidebarPageObject {
  constructor(private page: Page) {}

  async openAsideTask() {
    await this.page.getByTestId('TASK_LIST').click();
  }

  async createSimpleTask() {
    // Implementation details...
  }
}
```

#### API Clients

E2E tests use API clients to set up test data and verify backend state:

- The main `Clients` class in `apps/front-mynotary/e2e/clients/clients.ts` aggregates all specialized clients
- Each client (e.g., `OperationsClient`, `ContractsClient`) provides methods for specific API operations
- Clients use the authenticated Playwright `APIRequestContext` to make requests
- Test fixtures provide pre-configured clients to test cases

Example of client usage in a test:

```typescript
test('create a simple task', async ({ clients, page }) => {
  const operation = await clients.operations.createVenteAncien();
  // Test implementation using the created operation...
});
```

#### Test Fixtures

Playwright test fixtures are defined in `apps/front-mynotary/e2e/shared/fixtures.ts` and provide:

- Pre-configured API clients
- Environment configuration
- Authentication helpers
- Utility functions for common test operations

### Running E2E Tests

To run the tests locally, you need to run the following command:

```shell
pnpm e2e     # Run tests in headless mode
pnpm e2e-ui  # Run tests with UI mode for debugging
```

You can also run specific tests using the `--grep` option:

```shell
pnpm nx e2e front-mynotary --grep="task"
```

### Snapshot Testing

Snapshot testing is mainly used to test PDF content or text editing in the redaction page. To do so, we need to generate
a reference file (aka golden file) that will be used to compare the result of the test.

#### Update Reference Snapshots

To update the reference snapshots locally, you need to run the following command:

```shell
pnpm nx e2e front-mynotary --grep="pdf-content" --skip-nx-cache --update-snapshots
```

To update the reference snapshots on CI, you need to follow these steps:

1. Go to [Github actions](https://github.com/MyNotary/mynotary/actions) and start `Run pdf tests and update snapshots`
2. Click on the workflow and scroll down to download the artifact
3. Unzip the artifact and copy the `__snapshots__` folder to `apps/front-mynotary/e2e/tests/pdf`
4. Commit and push your changes to update the reference snapshots

### CI Integration

E2E tests are run as part of our CI pipeline:

- Tests are configured in `.github/workflows/test-e2e.yml`
- Tests are sharded to run in parallel for faster execution
- Blob reports are generated and can be downloaded as artifacts
- Special handling is in place for tests that require image processing (PDF snapshots)
- When tests fail, a GitHub Page is automatically deployed with detailed test results, making it easy to visualize
  errors and view screenshots of failed tests

## Best Practices

### General Testing Guidelines

- Write tests that are independent and can run in any order
- Follow the AAA pattern (Arrange, Act, Assert) for clarity
- Avoid hardcoded IDs or values in tests
- Keep tests focused on a single behavior or functionality
- Use descriptive test names that explain what is being tested
- Avoid test interdependencies
- Be careful not to test the same functionality multiple times across different test files. If you're using a service
  API from another scope that is already tested, spy on the API method you're using and verify it's called with the
  correct parameters rather than duplicating the test of the underlying functionality

### Backend Testing Best Practices

- **Prioritize wide tests** for most backend testing scenarios. Almost all backend tests should use a real database
  connection to ensure proper integration
- Only use unit tests for pure business logic and service methods that don't involve database operations

- When testing controllers, use the `createTestingWideApp` utility to ensure proper database integration

### End-to-End Testing Best Practices

- Focus on critical user journeys and workflows that span multiple components
- Keep E2E tests minimal and focused on integration points
- Use page objects to encapsulate UI interactions and provide a clean API for tests
- Use data-testid attributes for more stable selectors (we already do this extensively)
- Wait for network responses rather than arbitrary timeouts
- Use API clients to set up test data rather than UI interactions when possible
- Isolate tests by creating fresh data for each test
- Use the appropriate authentication profile for the test scenario
- For tests that modify global state (like credits), use `test.describe.configure({ mode: 'serial' })` to prevent race
  conditions
