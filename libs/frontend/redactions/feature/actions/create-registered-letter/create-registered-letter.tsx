import { ReactElement } from 'react';
import { MnButton } from '@mynotary/frontend/shared/ui';
import { useSelector } from 'react-redux';
import {
  useRegisteredLetterCreation,
  UseRegisteredLetterCreationMode
} from '@mynotary/frontend/registered-letters-creation/api';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { getFullSignaturesFiles, selectContractSignatureFiles } from '@mynotary/frontend/signatures/api';
import { map, size } from 'lodash';
import { findFiles } from '@mynotary/frontend/files/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { selectContract, selectContractPermission } from '@mynotary/frontend/legals/api';
import { useGlobalLoaderClass } from '@mynotary/frontend/shared/util';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import { getLegalContractTemplate } from '@mynotary/crossplatform/legal-contract-templates/api';

type CreateRegisteredLetterProps = {
  contractId: number;
};

const CreateRegisteredLetter = ({ contractId }: CreateRegisteredLetterProps): ReactElement | null => {
  const dispatch = useAsyncDispatch();
  const { isGlobalLoaderVisible } = useGlobalLoaderClass();

  const { handleLaunchRegisteredLetterDraft } = useRegisteredLetterCreation({ contractId });
  const canCreateRegisteredLetter = useSelector(
    selectContractPermission(PermissionType.CREATE_CONTRACT_REGISTERED_LETTER, contractId)
  );
  const contract = useSelector(selectContract(contractId));
  const signatureFiles = useSelector(
    selectContractSignatureFiles(contract?.operation.id, contract.id, contract.legalContractTemplateId)
  );
  const handleRegisteredLetter = async () => {
    assertNotNull(contract.contractFileId, 'contract.contractFileId');

    let filesIds: string[] = [contract.contractFileId];
    if (size(signatureFiles)) {
      filesIds = filesIds.concat(map(signatureFiles, (file) => file.id));
    }
    const fileInfos = await dispatch(findFiles(filesIds));
    const files = getFullSignaturesFiles(contract, signatureFiles, fileInfos);

    await handleLaunchRegisteredLetterDraft({
      contract,
      files,
      type: UseRegisteredLetterCreationMode.FROM_VALIDATED_CONTRACT
    });
  };

  if (
    !getLegalContractTemplate(contract.legalContractTemplateId).config.isMail ||
    contract.status !== ContractStatus.VALIDATED ||
    !canCreateRegisteredLetter
  ) {
    return null;
  }

  return (
    <MnButton
      className='rlps-button'
      disabled={isGlobalLoaderVisible}
      icon='/assets/images/pictos/icon/send.svg'
      label='Envoyer en recommandé élec.'
      labelPosition='left'
      onClick={handleRegisteredLetter}
      variant='secondary'
    />
  );
};

export { CreateRegisteredLetter };
