import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Task, TaskTypeAndReference } from '@mynotary/frontend/legals/core';

export interface SideBarState {
  action: SidebarAction | null;
  opened: boolean;
}

const initialState: SideBarState = {
  action: null,
  opened: false
};

export const sideBarSlice = createSlice({
  initialState,
  name: 'sideBar',
  reducers: {
    closeSidebar: (state) => {
      state.opened = false;
      state.action = null;
    },
    openSidebarAction: (state, action: PayloadAction<SidebarAction>) => {
      state.action = action.payload;
      state.opened = true;
    }
  }
});

export const { closeSidebar, openSidebarAction } = sideBarSlice.actions;

export interface SideBarSliceState {
  [sideBarSlice.name]: SideBarState;
}

export const selectSidebarFeature = (state: SideBarSliceState) => state[sideBarSlice.name];

export enum SidebarActionType {
  EMAILS = 'EMAILS123',
  ORDER = 'ORDER',
  PARTICIPANTS = 'PARTICIPANTS',
  TASK_CONTENT = 'TASK_CONTENT',
  TASK_CREATION = 'TASK_CREATION',
  TASK_EDITION = 'TASK_EDITION',
  TASK_LIST = 'TASK_LIST'
}
interface SideBarTaskCreation {
  legalComponentId: number;
  type: SidebarActionType.TASK_CREATION;
  typeAndReference: TaskTypeAndReference;
}

interface SideBarTaskEdition {
  task: Task;
  type: SidebarActionType.TASK_EDITION;
}

interface SideBarTaskContent {
  taskId: number;
  type: SidebarActionType.TASK_CONTENT;
}

interface SideBarTaskList {
  type: SidebarActionType.TASK_LIST;
}

interface SideBarParticipants {
  type: SidebarActionType.PARTICIPANTS;
}

interface SideBarOrders {
  type: SidebarActionType.ORDER;
}

interface SideBarEmails {
  type: SidebarActionType.EMAILS;
}

type SidebarAction =
  | SideBarTaskCreation
  | SideBarTaskEdition
  | SideBarTaskContent
  | SideBarTaskList
  | SideBarParticipants
  | SideBarOrders
  | SideBarEmails;
